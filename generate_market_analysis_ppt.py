#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
枸杞酒项目市场分析PPT生成器
使用python-pptx库生成专业的商业计划书PPT
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import os

def create_market_analysis_ppt():
    """创建市场分析PPT"""
    
    # 创建演示文稿
    prs = Presentation()
    
    # 设置幻灯片尺寸为16:9
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)
    
    # 定义颜色主题
    primary_color = RGBColor(31, 73, 125)    # 深蓝色
    secondary_color = RGBColor(79, 129, 189)  # 浅蓝色
    accent_color = RGBColor(192, 80, 77)      # 红色
    text_color = RGBColor(64, 64, 64)         # 深灰色
    
    # 1. 封面页
    slide1 = prs.slides.add_slide(prs.slide_layouts[6])  # 空白布局
    
    # 添加标题
    title_box = slide1.shapes.add_textbox(Inches(1), <PERSON>hes(2), <PERSON>hes(11.33), Inches(1.5))
    title_frame = title_box.text_frame
    title_frame.text = "枸杞酒项目商业计划书"
    title_para = title_frame.paragraphs[0]
    title_para.font.size = Pt(48)
    title_para.font.bold = True
    title_para.font.color.rgb = primary_color
    title_para.alignment = PP_ALIGN.CENTER
    
    # 添加副标题
    subtitle_box = slide1.shapes.add_textbox(Inches(1), Inches(3.8), Inches(11.33), Inches(1))
    subtitle_frame = subtitle_box.text_frame
    subtitle_frame.text = "市场分析部分"
    subtitle_para = subtitle_frame.paragraphs[0]
    subtitle_para.font.size = Pt(36)
    subtitle_para.font.color.rgb = secondary_color
    subtitle_para.alignment = PP_ALIGN.CENTER
    
    # 添加提交信息
    info_box = slide1.shapes.add_textbox(Inches(1), Inches(5.5), Inches(11.33), Inches(1))
    info_frame = info_box.text_frame
    info_frame.text = "提交给：厚生记董事长\n2024年8月"
    info_para = info_frame.paragraphs[0]
    info_para.font.size = Pt(18)
    info_para.font.color.rgb = text_color
    info_para.alignment = PP_ALIGN.CENTER
    
    # 2. 目录页
    slide2 = prs.slides.add_slide(prs.slide_layouts[1])  # 标题和内容布局
    slide2.shapes.title.text = "目录"
    
    content = slide2.shapes.placeholders[1].text_frame
    content.text = """一、市场概况与机遇
二、目标市场分析
三、竞争格局分析
四、市场规模测算
五、市场趋势与机遇
六、市场进入策略建议
七、结论与建议"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(24)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(12)
    
    # 3. 市场概况与机遇
    slide3 = prs.slides.add_slide(prs.slide_layouts[1])
    slide3.shapes.title.text = "一、市场概况与机遇"
    
    content = slide3.shapes.placeholders[1].text_frame
    content.text = """保健酒市场现状
• 劲酒占据88.72%市场份额，寡头垄断格局
• 行业龙头面临增长天花板，向酱酒转型
• 为新进入者提供市场空间

养生酒赛道崛起
• 市场规模580亿元，迎来高质量发展机遇
• 护肝功能性食品复合增长率5.5%
• 中国护肝市场：101.7亿→116.43亿元

消费升级驱动
• 健康意识显著提升，话题热度增长40.5%
• 中高端功能性酒类符合消费升级趋势"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(8)
    
    # 4. 目标市场分析 - 男性市场
    slide4 = prs.slides.add_slide(prs.slide_layouts[1])
    slide4.shapes.title.text = "二、目标市场分析 - 男性市场（35-55岁）"
    
    content = slide4.shapes.placeholders[1].text_frame
    content.text = """市场规模
• 白酒重度用户平均年龄超过45岁
• 45岁以上人群占比68.5%，构成稳定消费基础

用户特征
• 运动量少，工作应酬频繁
• 精神状态欠佳，生活工作压力大
• 健康意识觉醒，消费能力强

消费行为
• 强社交产品偏好，商务应酬需求旺盛
• 线上化消费趋势明显
• 对功能性、品质化产品接受度高

市场机会：中老年白酒消费健康化趋势明显"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(8)
    
    # 5. 目标市场分析 - 女性市场
    slide5 = prs.slides.add_slide(prs.slide_layouts[1])
    slide5.shapes.title.text = "二、目标市场分析 - 女性市场（25-40岁）"
    
    content = slide5.shapes.placeholders[1].text_frame
    content.text = """市场规模
• 微醺经济快速发展
• 2024年Q1抖音果酒销售额同比增长88%

用户特征
• 关注美容养颜与健康管理
• 追求精致生活方式
• 微醺文化接受度高

消费行为
• 68%为悦己消费，注重产品颜值和体验
• 最关注消费场景与品类创新
• 社交电商渠道活跃度高

市场潜力：梅见零售规模已达25亿元"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(8)
    
    # 6. 竞争格局分析
    slide6 = prs.slides.add_slide(prs.slide_layouts[1])
    slide6.shapes.title.text = "三、竞争格局分析"
    
    content = slide6.shapes.placeholders[1].text_frame
    content.text = """直接竞品分析

劲酒（男性对标）
• 保健酒市场绝对龙头，88.72%市场份额
• 价格体系：¥15-1088全价格段覆盖
• 挑战：面临增长天花板，正向酱酒转型

果立方（女性对标）
• 江小白旗下品牌，主攻果味酒市场
• 2024年梅见+果立方业务增长30%
• 风险：营销争议事件影响品牌形象

竞争优势
• 宁夏枸杞权威认证，道地性背书
• 性别化功能性酒类差异定位
• 技术创新空间巨大（酶解、发酵技术）"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(8)
    
    # 7. 市场规模测算
    slide7 = prs.slides.add_slide(prs.slide_layouts[1])
    slide7.shapes.title.text = "四、市场规模测算"
    
    content = slide7.shapes.placeholders[1].text_frame
    content.text = """目标市场容量

男性市场
• 35-55岁男性中高端消费人群：2700万人
• 按1%渗透率计算：潜在用户27万人

女性市场
• 25-40岁女性中高端消费人群：300万人
• 按0.5%渗透率计算：潜在用户1.5万人

市场规模预测（5年期）
• 保守估算：1.47亿元
  - 男性产品：27万用户 × 500元/年 = 1.35亿元
  - 女性产品：1.5万用户 × 800元/年 = 0.12亿元
• 乐观估算：3亿元以上（按15%复合增长率）"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(8)
    
    # 8. 市场趋势与机遇
    slide8 = prs.slides.add_slide(prs.slide_layouts[1])
    slide8.shapes.title.text = "五、市场趋势与机遇"
    
    content = slide8.shapes.placeholders[1].text_frame
    content.text = """消费趋势
• 健康化：Z世代贡献35.9%市场增量
• 个性化：消费者更关注场景与品类匹配
• 便捷化：即时零售增长554%
• 品质化：中高端产品需求持续增长

技术趋势
• 药食同源：传统成分现代化应用
• 生物技术：发酵、酶解技术提升功效
• 个性定制：基于用户画像的精准开发

渠道趋势
• 线上主导：电商平台成为主要销售渠道
• 即时零售：满足即时消费需求
• 社交电商：特别适合女性产品推广"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(8)
    
    # 9. 市场进入策略建议
    slide9 = prs.slides.add_slide(prs.slide_layouts[1])
    slide9.shapes.title.text = "六、市场进入策略建议"
    
    content = slide9.shapes.placeholders[1].text_frame
    content.text = """市场定位
• 男性产品：商务人士的健康伴侣
• 女性产品：精致女性的美颜秘籍

差异化策略
• 功能差异化：性别定向的复合功能设计
• 原料差异化：宁夏道地枸杞权威背书
• 文化差异化：传承千年的养生智慧
• 体验差异化：从包装到口感的全方位设计

风险控制
• 合规风险：严格按法规进行功效宣传
• 竞争风险：建立差异化壁垒
• 品牌风险：建立正确价值观
• 供应链风险：与优质供应商长期合作"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(8)
    
    # 10. 结论与建议
    slide10 = prs.slides.add_slide(prs.slide_layouts[1])
    slide10.shapes.title.text = "七、结论与建议"
    
    content = slide10.shapes.placeholders[1].text_frame
    content.text = """核心优势
• 市场机遇明确：养生酒赛道快速增长
• 差异化定位清晰：性别化功能性酒类蓝海
• 原料优势突出：宁夏枸杞权威认证
• 消费趋势支撑：健康化、个性化需求增长

董事长重点关注
• 产品合规性确保
• 差异化竞争壁垒构建
• 渠道布局的前瞻性规划
• 品牌价值观的正确建立

投资建议
市场分析表明，该项目具备良好的商业前景和投资价值，
建议加快推进项目实施。"""
    
    for paragraph in content.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.color.rgb = text_color
        paragraph.space_after = Pt(8)
    
    # 11. 谢谢页
    slide11 = prs.slides.add_slide(prs.slide_layouts[6])  # 空白布局
    
    thanks_box = slide11.shapes.add_textbox(Inches(1), Inches(3), Inches(11.33), Inches(1.5))
    thanks_frame = thanks_box.text_frame
    thanks_frame.text = "谢谢聆听"
    thanks_para = thanks_frame.paragraphs[0]
    thanks_para.font.size = Pt(48)
    thanks_para.font.bold = True
    thanks_para.font.color.rgb = primary_color
    thanks_para.alignment = PP_ALIGN.CENTER
    
    # 保存PPT
    output_file = "枸杞酒项目市场分析.pptx"
    prs.save(output_file)
    print(f"PPT已生成：{output_file}")
    
    return output_file

def install_requirements():
    """安装所需依赖"""
    import subprocess
    import sys
    
    try:
        import pptx
        print("python-pptx 已安装")
    except ImportError:
        print("正在安装 python-pptx...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "python-pptx"])
        print("python-pptx 安装完成")

if __name__ == "__main__":
    print("=== 枸杞酒项目市场分析PPT生成器 ===")
    
    # 检查并安装依赖
    install_requirements()
    
    # 生成PPT
    try:
        output_file = create_market_analysis_ppt()
        print(f"\n✅ PPT生成成功！")
        print(f"📁 文件位置：{os.path.abspath(output_file)}")
        print(f"📊 包含11张幻灯片，专业商务风格")
        print(f"🎯 适合董事长汇报使用")
    except Exception as e:
        print(f"❌ 生成失败：{e}")
